import Branding from "@/components/blocks/branding";
import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
import Hero from "@/components/blocks/hero";
import HeroWithCompare from "@/components/blocks/hero-with-compare";
import ImageCompareGallery from "@/components/blocks/image-compare-gallery";
import Showcase from "@/components/blocks/showcase";
import Stats from "@/components/blocks/stats";
import Testimonial from "@/components/blocks/testimonial";
import { getLandingPage } from "@/services/page";
import KontextDev from "@/components/kontextdev";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export const runtime = "edge";

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}) {
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/photo-colorization`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/photo-colorization`;
  }

  return {
    title: "Photo Colorization - Colorize Black & White Photos in Seconds",
    description: "Photo Colorization made easy - colorize black & white photos online in seconds. Restore old images and bring cherished memories to life with vivid colors.",
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function PhotoColorizationPage({
  params: { locale },
}: {
  params: { locale: string };
}) {
  const page = await getLandingPage(locale);

  // Photo colorization example comparison images
  const compareImages = {
    originalSrc: "https://pic.kontext-dev.com/black-white-photo-before.webp",
    modifiedSrc: "https://pic.kontext-dev.com/colorized-photo-after.webp",
    beforeText: "Black and white photo",
    afterText: "Colorized photo"
  };

  // More photo colorization examples
  const compareGroups = [
    {
      id: 1,
      originalSrc: "https://pic.kontext-dev.com/vintage-black-white-portrait.webp",
      modifiedSrc: "https://pic.kontext-dev.com/colorized-vintage-portrait.webp",
      alt: "Colorize black and white portrait photo",
      beforeText: "Original black and white",
      afterText: "Colorized photo"
    },
    {
      id: 2,
      originalSrc: "https://pic.kontext-dev.com/old-family-photo-bw.webp",
      modifiedSrc: "https://pic.kontext-dev.com/colorized-family-photo.webp",
      alt: "Colorize old family photo",
      beforeText: "Original black and white",
      afterText: "Colorized photo"
    },
    {
      id: 3,
      originalSrc: "https://pic.kontext-dev.com/historical-photo-bw.webp",
      modifiedSrc: "https://pic.kontext-dev.com/colorized-historical-photo.webp",
      alt: "Colorize historical black and white photo",
      beforeText: "Original black and white",
      afterText: "Colorized photo"
    },
    {
      id: 4,
      originalSrc: "https://pic.kontext-dev.com/vintage-landscape-bw.webp",
      modifiedSrc: "https://pic.kontext-dev.com/colorized-landscape.webp",
      alt: "Colorize vintage landscape photo",
      beforeText: "Original black and white",
      afterText: "Colorized photo"
    }
  ];

  return (
    <>
      

      {/* Hero section with comparison */}
      {page.hero && <HeroWithCompare
        hero={{
          ...page.hero,
          title: "Photo Colorization Tool - Colorize Images & Pictures in Seconds",
          description: "Our Photo Colorization tool helps you colorize photos, images, and pictures with AI-powered precision. Whether you're colorizing old photos, restoring family memories, or adding life to black and white pictures, this tool delivers vibrant, natural results. Easily colorize a photo or image online in seconds - no editing skills required.",
          buttons: [
            {
              ...page.hero.buttons?.[0],
              title: "Colorize Photo Now",
              url: "#colorize-photo"
            },
            {
              ...page.hero.buttons?.[1],
              title: "View Examples",
              url: "#examples"
            }
          ],
          show_happy_users: false
        }}
        compareImages={compareImages}
      />}

      {/* 使用增强后的KontextDev组件，配置为照片上色专用模式 */}
      <div id="colorize-photo" className="text-center py-8">
        <h2 className="text-3xl font-bold">AI Photo Colorization Tool</h2>
        <p className="text-xl text-muted-foreground mt-2 mb-8">
          Upload your black and white photo and our AI photo colorizer will automatically colorize images with realistic colors
        </p>
      </div>

      <KontextDev
        title="Photo Colorization"
        showPromptInput={false}
        showTranslation={false}
        showQuickActions={false}
        defaultPrompt="Colorize this black and white photo with realistic colors"
        generateButtonText="Colorize Photo"
        resultTitle="Photo Colorization Result"
        disableTranslation={true}
      />

      {/* Photo colorization gallery */}
      <div id="examples">
        <ImageCompareGallery
          title="AI Photo Colorization Examples"
          description="Discover how our advanced photo colorization tool transforms black and white photos into stunning colorized images. See colorizing photos, colorizing old photos, and colorizing black and white photos with professional results"
          compareGroups={compareGroups}
        />
      </div>

      {/* Features introduction */}
      {page.introduce && <Feature1 section={{
        ...page.introduce,
        title: "Why Choose Our Photo Colorization Tool",
        description: "Our AI-powered photo colorization tool provides advanced technology to colorize photos and transform black and white images into vibrant, realistic colorized pictures. Experience professional photo colorization with our cutting-edge photo colorization technology.",
        image: {
          src: "https://pic.kontext-dev.com/photo-colorization-before-after.webp",
          alt: "Photo colorization features"
        },
        items: [
          {
            title: "Smart Color Recognition",
            description: "Our photo colorization tool uses advanced AI algorithms to analyze image content and apply realistic colors that match the original scene and objects. This photo colorization feature ensures accurate color placement.",
            icon: "RiSearchLine"
          },
          {
            title: "Original Quality Preservation",
            description: "The photo colorization technology maintains original image quality while adding vibrant colors, preserving all important details and textures in your colorized photos. Our photo colorization process never compromises image clarity.",
            icon: "RiImageLine"
          },
          {
            title: "Multiple Photo Types Support",
            description: "Our photo colorizer effectively handles various types of black and white photos including portraits, landscapes, historical photos, and vintage family pictures. Photo colorization works perfectly for all image types.",
            icon: "RiImageEditLine"
          },
          {
            title: "Professional Results",
            description: "Experience professional-grade photo colorization with realistic color application that brings your old photos to life with stunning visual impact. Our photo colorization delivers museum-quality results.",
            icon: "RiVipCrownLine"
          }
        ]
      }} />}

      {/* Benefits section */}
      {page.benefit && <Feature2 section={{
        ...page.benefit,
        title: "Benefits of Using Our Photo Colorization Tool",
        description: "Our photo colorization tool offers several advantages for transforming your black and white photos into colorized masterpieces. Discover why our photo colorization service is the preferred choice for professional photo colorization.",
        items: [
          {
            title: "Time-Efficient Processing",
            description: "Colorize photos in seconds instead of spending hours with manual photo coloring techniques",
            icon: "RiTimeLine",
            image: {
              src: "https://pic.kontext-dev.com/photo-colorization-before-after.webp",
              alt: "Time-Efficient Photo Colorization"
            }
          },
          {
            title: "Professional-Grade Results",
            description: "Get stunning, realistic colorized photos suitable for family albums, historical preservation, and professional projects",
            icon: "RiMedalLine",
            image: {
              src: "https://pic.kontext-dev.com/professional-colorization-results.webp",
              alt: "Professional-Grade Photo Colorization Results"
            }
          },
          {
            title: "User-Friendly Interface",
            description: "Simple interface designed for users of all skill levels - colorize images without technical complexity in photo colorization",
            icon: "RiUserSmileLine",
            image: {
              src: "https://pic.kontext-dev.com/vintage-portrait-colorization-example.webp",
              alt: "Vintage portrait before and after using Photo Colorization by Kontext Dev, showing realistic AI colorization result"
            }
          },
          {
            title: "Data Privacy Protection",
            description: "Your photos are processed securely and not stored after photo colorization is complete, ensuring your memories remain private",
            icon: "RiShieldLine",
            image: {
              src: "https://pic.kontext-dev.com/family-photo-colorization-example.webp",
              alt: "Family photo before and after using Photo Colorization by Kontext Dev, AI realistic color result"
            }
          }
        ]
      }} />}

      {/* Usage steps */}
      {page.usage && <Feature3 section={{
        ...page.usage,
        title: "How to Use Our Photo Colorization Tool",
        description: "Colorize photos and transform black and white images in just four simple steps with our AI photo colorizer",
        items: [
          {
            title: "Upload Your Photo",
            description: "Select and upload a black and white photo or image that you want to colorize",
            icon: "RiUploadLine"
          },
          {
            title: "AI Photo Analysis",
            description: "Our AI will automatically analyze the image content and identify objects, textures, and scenes for optimal photo colorization",
            icon: "RiScanLine"
          },
          {
            title: "Preview Colorized Result",
            description: "Check the preview to see how our photo colorization has transformed your black and white photo with realistic colors",
            icon: "RiEyeLine"
          },
          {
            title: "Download Colorized Photo",
            description: "Download your beautifully colorized photo with vibrant, realistic colors for immediate use and sharing",
            icon: "RiDownloadLine"
          }
        ]
      }} />}

      {/* Key Features section */}
      {page.feature && <Feature section={{
        ...page.feature,
        title: "Key Features of Our Photo Colorization Tool",
        description: "Discover the powerful capabilities that make our photo colorization tool the best choice for colorizing photos and transforming black and white images. Our photo colorization features ensure superior photo colorization results every time.",
        items: [
          {
            title: "Advanced AI Algorithm",
            description: "Our photo colorization tool utilizes state-of-the-art AI algorithms to intelligently analyze image content and apply realistic colors while preserving original photo quality. This photo colorization technology represents the latest in AI innovation.",
            icon: "RiAiGenerate"
          },
          {
            title: "Multi-Type Photo Support",
            description: "The photo colorization service can handle portraits, landscapes, historical photos, vintage images, and more - colorizing photos of various types with excellent results. Photo colorization works seamlessly across all photo categories.",
            icon: "RiImageEditLine"
          },
          {
            title: "High Resolution Processing",
            description: "Our photo colorization tool processes high-resolution images without quality loss, ensuring your colorized photos maintain their clarity and detail after photo colorization. High-quality photo colorization is our specialty.",
            icon: "RiImageLine"
          },
          {
            title: "Intelligent Color Application",
            description: "With our photo colorization technology, the AI intelligently applies colors based on object recognition, ensuring natural and realistic colorized photos with proper color schemes. Smart photo colorization delivers authentic results.",
            icon: "RiPaintBrushLine"
          },
          {
            title: "Fast Processing Speed",
            description: "The photo colorization service quickly colorizes images with our optimized algorithms that deliver stunning colorized photos in seconds rather than minutes, saving you valuable time. Rapid photo colorization without compromising quality.",
            icon: "RiTimerFlashLine"
          },
          {
            title: "Cloud-Based Solution",
            description: "Our photo colorization tool operates in the cloud, so there's no need to download software or worry about your device's processing power for photo colorization. Cloud-based photo colorization ensures accessibility anywhere.",
            icon: "RiCloudLine"
          }
        ]
      }} />}

      {/* Stats section - REMOVED */}

      {/* Pricing Section */}
      <div className="container py-12 text-center">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold tracking-tight mb-4">Need More Advanced Photo Colorization Features?</h2>
          <p className="text-xl text-muted-foreground mb-8">
            Upgrade to our premium photo colorization service for batch processing, higher resolution support, and priority photo colorization processing
          </p>
          <Link href="/pricing">
            <Button size="lg" className="gap-2">
              View Premium Photo Colorization Plans
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M5 12h14" />
                <path d="m12 5 7 7-7 7" />
              </svg>
            </Button>
          </Link>
        </div>
      </div>

      {/* Testimonials */}
      {page.testimonial && <Testimonial section={{
        ...page.testimonial,
        title: "What Users Say About Our Photo Colorization Tool",
        description: "Hear from photographers, historians, and families who use our AI photo colorization tool to bring their memories to life. Read real testimonials about our photo colorization service and photo colorization results.",
        items: [
          {
            description: "This photo colorization tool brought my grandmother's old photos to life! I had a collection of black and white family photos, and the AI colorized them beautifully with realistic colors.",
            title: "Sarah J.",
            label: "Family Historian",
            image: {
              src: "https://pic.kontext-dev.com/1.webp",
              alt: "Sarah J., Family Historian"
            }
          },
          {
            description: "I was amazed by the photo colorization results. The AI perfectly colorized my vintage portrait photos with natural skin tones and realistic colors. The photo colorization quality exceeded my expectations.",
            title: "Michael T.",
            label: "Photographer",
            image: {
              src: "https://pic.kontext-dev.com/3.webp",
              alt: "Michael T., Photographer"
            }
          },
          {
            description: "As a content creator, I often need to colorize old photos for historical content. This photo colorization tool makes it quick and easy, with professional colorized photos every time.",
            title: "Lisa R.",
            label: "Content Creator",
            image: {
              src: "https://pic.kontext-dev.com/2.webp",
              alt: "Lisa R., Content Creator"
            }
          },
          {
            description: "Our museum uses this photo colorization service to bring historical photos to life for exhibitions. The ability to colorize images with such accuracy has enhanced our visitor experience significantly.",
            title: "David K.",
            label: "Museum Director",
            image: {
              src: "https://pic.kontext-dev.com/4.webp",
              alt: "David K., Museum Director"
            }
          },
          {
            description: "The photo colorization feature has been perfect for my genealogy blog. I can now colorize pictures of ancestors and share vibrant, colorized photos that truly capture family history.",
            title: "Emma S.",
            label: "Genealogy Blogger",
            image: {
              src: "https://pic.kontext-dev.com/6.webp",
              alt: "Emma S., Genealogy Blogger"
            }
          },
          {
            description: "I've tried several photo colorization tools, but this one stands out for its realistic results. It handles even complex vintage photos well, creating beautiful colorized images with natural colors.",
            title: "Robert M.",
            label: "Digital Artist",
            image: {
              src: "https://pic.kontext-dev.com/5.webp",
              alt: "Robert M., Digital Artist"
            }
          }
        ]
      }} />}

      {/* FAQ section */}
      {page.faq && <FAQ section={{
        ...page.faq,
        title: "FAQ About Photo Colorization Tool",
        description: "Common questions about our photo colorization tool and AI photo colorization technology. Get answers about photo colorization process, photo colorization quality, and photo colorization features.",
        items: [
          {
            title: "How does the photo colorization process work?",
            description: "Our photo colorization tool uses advanced AI algorithms to analyze black and white photos and intelligently apply realistic colors. The AI recognizes objects, textures, and scenes to colorize photos with natural, historically accurate colors."
          },
          {
            title: "What types of photos work best for photo colorization?",
            description: "Our photo colorization tool works excellently with portraits, family photos, historical images, vintage photographs, and landscape photos. The photo colorizer can handle various black and white photo types and colorize images with professional results."
          },
          {
            title: "How is the image quality after photo colorization?",
            description: "We use advanced AI technology in our photo colorization tool to maintain original image quality while adding vibrant colors. Our algorithms preserve all details and textures in your photos during the photo colorization process."
          },
          {
            title: "Are there file size limitations for photo colorization?",
            description: "The photo colorization tool supports images up to 10MB in size. Each photo colorization uses 5 credits. Currently, our photo colorization service processes one image at a time for optimal quality results."
          },
          {
            title: "How long does photo colorization take?",
            description: "Most photos are colorized within 10-30 seconds using our photo colorization tool, depending on the complexity and size of the image. Our cloud-based photo colorization ensures fast results without taxing your device."
          },
          {
            title: "Can I use colorized photos commercially?",
            description: "You can use the colorized photos for any purpose as long as you have the rights to the original black and white photo. Our photo colorization service only adds colors to your existing images without changing ownership rights."
          },
          {
            title: "What file formats does the photo colorization tool support?",
            description: "Our photo colorization tool works best with JPEG, PNG, and WebP formats. The photo colorizer can process various types of black and white photos, vintage images, historical photographs, and scanned pictures."
          },
          {
            title: "Is my data safe when using photo colorization?",
            description: "We take data privacy seriously. Photos uploaded to our photo colorization tool are used only for colorization processing and are not stored on our servers after the photo colorization is complete. We adhere to strict privacy policies."
          }
        ]
      }} />}

      {/* Call to action */}
      {page.cta && <CTA section={{
        ...page.cta,
        title: "Try Our Photo Colorization Tool Now",
        description: "Colorize photos and transform your black and white images with our AI-powered photo colorization tool - get stunning colorized photos in seconds",
        buttons: [
          {
            ...page.cta.buttons?.[0],
            title: "Start Photo Colorization",
            url: "#colorize-photo"
          }
        ]
      }} />}
    </>
  );
} 